import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { 
  Typography, 
  ScrollArea,
  Avatar,
  Button,
  Textarea
} from '@/shared/components/common';
import { formatTimestamp } from '@/shared/utils/form-date-utils';

import { TodoCommentResponseDto } from '../../types/comment.types';

interface TodoCommentsSectionProps {
  taskId: number;
  comments: TodoCommentResponseDto[];
  isLoading: boolean;
  onAddComment: (contentHtml: string) => void;
  onRefresh: () => void;
}

/**
 * Todo comments section component for new API
 */
const TodoCommentsSection: React.FC<TodoCommentsSectionProps> = ({
  taskId,
  comments,
  isLoading,
  onAddComment,
  onRefresh,
}) => {
  const { t } = useTranslation(['todolist', 'common']);
  const [commentText, setCommentText] = useState('');

  const handleSendComment = () => {
    if (commentText.trim()) {
      // Convert plain text to HTML for now
      const htmlContent = `<p>${commentText.trim()}</p>`;
      onAddComment(htmlContent);
      setCommentText('');
    }
  };

  const renderCommentContent = (comment: TodoCommentResponseDto) => {
    if (comment.isSystemEvent && comment.eventData) {
      // Render system event
      return (
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-800">
          <Typography variant="body2" className="text-blue-800 dark:text-blue-200">
            {t('todolist:task.comments.systemEvent', 'System Event')}: {comment.eventData.eventType}
          </Typography>
          {comment.eventData.oldValue && comment.eventData.newValue && (
            <Typography variant="caption" className="text-blue-600 dark:text-blue-300">
              {comment.eventData.oldValue} → {comment.eventData.newValue}
            </Typography>
          )}
        </div>
      );
    }

    // Render regular comment
    return (
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
        <div className="flex items-center space-x-2 mb-1">
          <Typography variant="subtitle2" className="font-medium">
            {comment.userId ? `User ${comment.userId}` : 'System'}
          </Typography>
          <Typography variant="caption" className="text-gray-500">
            {formatTimestamp(comment.createdAt || 0)}
          </Typography>
          {comment.commentType && (
            <span className="px-2 py-1 text-xs bg-gray-200 dark:bg-gray-700 rounded">
              {comment.commentType}
            </span>
          )}
        </div>
        <div 
          className="prose prose-sm dark:prose-invert max-w-none"
          dangerouslySetInnerHTML={{ __html: comment.contentHtml }}
        />
        {comment.resources && comment.resources.length > 0 && (
          <div className="mt-2 space-y-1">
            <Typography variant="caption" className="text-gray-500">
              {t('todolist:task.comments.attachments', 'Attachments')}:
            </Typography>
            {comment.resources.map((resource, index) => (
              <div key={index} className="flex items-center space-x-2">
                <span className="text-xs bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded">
                  {resource.type}
                </span>
                <a 
                  href={resource.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  {resource.name}
                </a>
              </div>
            ))}
          </div>
        )}
        {comment.mentions && comment.mentions.length > 0 && (
          <div className="mt-2">
            <Typography variant="caption" className="text-gray-500">
              {t('todolist:task.comments.mentions', 'Mentions')}:
            </Typography>
            <div className="flex flex-wrap gap-1 mt-1">
              {comment.mentions.map((mention, index) => (
                <span 
                  key={index}
                  className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded"
                >
                  @{mention.username}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full">
      {/* Messages area */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full p-4">
          {comments.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center py-8">
              <div className="text-4xl mb-4">💬</div>
              <Typography variant="h6" className="mb-2">
                {t('todolist:task.comments.empty', 'No comments yet')}
              </Typography>
              <Typography variant="body2" className="text-gray-500">
                {t('todolist:task.comments.emptyDescription', 'Be the first to comment!')}
              </Typography>
            </div>
          ) : (
            <div className="space-y-4">
              {comments.map((comment) => (
                <div key={comment.id} className="flex space-x-3">
                  <Avatar
                    src={undefined}
                    alt={comment.userId ? `User ${comment.userId}` : 'System'}
                    size="sm"
                  />
                  <div className="flex-1">
                    {renderCommentContent(comment)}
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </div>

      {/* Input area */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-4">
        <div className="flex space-x-2">
          <div className="flex-1">
            <Textarea
              value={commentText}
              onChange={(e) => setCommentText(e.target.value)}
              placeholder={t('todolist:task.comments.placeholder', 'Write a comment...')}
              rows={2}
              className="resize-none"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendComment();
                }
              }}
            />
          </div>
          <Button
            onClick={handleSendComment}
            disabled={!commentText.trim()}
            size="sm"
            variant="primary"
          >
            {t('todolist:task.comments.send', 'Send')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TodoCommentsSection;
